'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface TerminalLine {
  type: 'command' | 'output' | 'error' | 'success'
  content: string
  timestamp?: Date
}

interface PortfolioSection {
  id: string
  title: string
  content: string
  commands?: string[]
}

const portfolioSections: PortfolioSection[] = [
  {
    id: 'about',
    title: 'About Me',
    content: `Name: Aniket Batabyal
Role: Full Stack Developer
Location: India
Email: <EMAIL>

I'm a passionate full-stack developer with expertise in modern web technologies. 
I love building scalable applications and exploring new technologies.`,
    commands: ['about', 'whoami']
  },
  {
    id: 'skills',
    title: 'Skills',
    content: `Frontend:
  • React/Next.js
  • TypeScript
  • Tailwind CSS
  • HTML5/CSS3
  • JavaScript/ES6+

Backend:
  • Node.js
  • Python
  • PostgreSQL
  • MongoDB
  • REST APIs

Tools:
  • Git/GitHub
  • Docker
  • AWS
  • VS Code
  • Figma`,
    commands: ['skills', 'tech']
  },
  {
    id: 'projects',
    title: 'Projects',
    content: `1. E-Commerce Platform
   • Full-stack application with React and Node.js
   • Real-time inventory management
   • Payment integration with Stripe

2. Task Management System
   • Collaborative task management tool
   • Real-time updates with WebSocket
   • Drag-and-drop interface

3. Weather Dashboard
   • Real-time weather data visualization
   • Interactive maps and charts
   • Location-based forecasts`,
    commands: ['projects', 'work']
  },
  {
    id: 'contact',
    title: 'Contact',
    content: `Let's connect! Feel free to reach out:

GitHub: https://github.com/anarchymonkey
LinkedIn: https://www.linkedin.com/in/aniket-batabyal-801460134/
Email: <EMAIL>

I'm always open to discussing new opportunities and interesting projects.`,
    commands: ['contact', 'social']
  }
]

export default function TerminalPortfolio() {
  const [input, setInput] = useState('')
  const [terminalLines, setTerminalLines] = useState<TerminalLine[]>([
    {
      type: 'success',
      content: 'Welcome to Aniket Batabyal\'s Terminal Portfolio',
      timestamp: new Date()
    },
    {
      type: 'output',
      content: 'Type "help" to see available commands',
      timestamp: new Date()
    },
    {
      type: 'output',
      content: '',
      timestamp: new Date()
    }
  ])
  const [currentSection, setCurrentSection] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const [cursorVisible, setCursorVisible] = useState(true)
  const inputRef = useRef<HTMLInputElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)

  // Cursor blinking effect
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setCursorVisible(v => !v)
    }, 500)
    return () => clearInterval(cursorInterval)
  }, [])

  // Auto-scroll to bottom
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [terminalLines])

  // Focus input on load
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const addTerminalLine = (line: TerminalLine) => {
    setTerminalLines(prev => [...prev, { ...line, timestamp: new Date() }])
  }

  const typeText = async (text: string, type: TerminalLine['type'] = 'output') => {
    setIsTyping(true)
    let currentText = ''
    
    for (let i = 0; i <= text.length; i++) {
      currentText = text.substring(0, i)
      setTerminalLines(prev => {
        const newLines = [...prev]
        if (newLines.length > 0 && newLines[newLines.length - 1].type === 'typing') {
          newLines[newLines.length - 1] = { type: 'typing', content: currentText }
        } else {
          newLines.push({ type: 'typing', content: currentText })
        }
        return newLines
      })
      await new Promise(resolve => setTimeout(resolve, 20))
    }
    
    // Replace typing line with final line
    setTerminalLines(prev => {
      const newLines = prev.filter(line => line.type !== 'typing')
      return [...newLines, { type, content: text }]
    })
    
    setIsTyping(false)
  }

  const executeCommand = async (command: string) => {
    const cmd = command.trim().toLowerCase()
    
    // Add command to terminal
    addTerminalLine({ type: 'command', content: `$ ${command}` })
    
    if (cmd === '' || cmd === 'clear') {
      if (cmd === 'clear') {
        setTerminalLines([
          {
            type: 'success',
            content: 'Terminal cleared',
            timestamp: new Date()
          }
        ])
      }
      return
    }

    if (cmd === 'help') {
      const helpText = `Available Commands:
  help     - Show this help message
  about    - About me
  skills   - My technical skills
  projects - My projects
  contact  - Contact information
  clear    - Clear terminal
  whoami   - Display current user
  social   - Social media links
  tech     - Technology stack
  work     - Portfolio projects`
      await typeText(helpText, 'output')
      return
    }

    if (cmd === 'whoami') {
      await typeText('aniket', 'success')
      return
    }

    // Check for section commands
    const section = portfolioSections.find(s => 
      s.commands?.includes(cmd) || s.id === cmd
    )

    if (section) {
      setCurrentSection(section.id)
      await typeText(section.title, 'success')
      await new Promise(resolve => setTimeout(resolve, 200))
      await typeText(section.content, 'output')
      return
    }

    // Easter eggs
    if (cmd === 'ls') {
      await typeText('about/  skills/  projects/  contact/', 'output')
      return
    }

    if (cmd === 'pwd') {
      await typeText('/home/<USER>/portfolio', 'output')
      return
    }

    if (cmd === 'date') {
      await typeText(new Date().toString(), 'output')
      return
    }

    // Unknown command
    await typeText(`Command not found: ${cmd}. Type 'help' for available commands.`, 'error')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isTyping) return
    
    const command = input.trim()
    setInput('')
    
    if (command) {
      await executeCommand(command)
    }
  }

  const handleQuickCommand = async (command: string) => {
    if (isTyping) return
    setInput(command)
    await executeCommand(command)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 flex items-center justify-center">
      <div className="w-full max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold text-green-400 mb-2 font-mono">
            Aniket Batabyal
          </h1>
          <p className="text-gray-400 text-lg font-mono">Full Stack Developer</p>
        </div>

        {/* Terminal Window */}
        <Card className="bg-black border-green-500/30 shadow-2xl shadow-green-500/10 overflow-hidden">
          {/* Terminal Header */}
          <div className="bg-gray-800 px-4 py-2 flex items-center justify-between border-b border-green-500/30">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="text-green-400 text-sm font-mono">
              terminal-portfolio
            </div>
            <div className="w-16"></div>
          </div>

          {/* Terminal Body */}
          <div 
            ref={terminalRef}
            className="h-96 md:h-[500px] overflow-y-auto bg-black p-4 font-mono text-sm space-y-1"
          >
            {terminalLines.map((line, index) => (
              <div key={index} className="flex items-start space-x-2">
                <span className="text-green-500/50 text-xs mt-0.5 min-w-[45px]">
                  {line.timestamp && formatTime(line.timestamp)}
                </span>
                <div className="flex-1">
                  {line.type === 'command' && (
                    <span className="text-green-400">{line.content}</span>
                  )}
                  {line.type === 'output' && (
                    <span className="text-gray-300 whitespace-pre-line">{line.content}</span>
                  )}
                  {line.type === 'error' && (
                    <span className="text-red-400">{line.content}</span>
                  )}
                  {line.type === 'success' && (
                    <span className="text-green-400">{line.content}</span>
                  )}
                  {line.type === 'typing' && (
                    <span className="text-gray-300">
                      {line.content}
                      <span className={`inline-block w-2 h-4 bg-green-400 ml-1 ${cursorVisible ? 'opacity-100' : 'opacity-0'}`}></span>
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Terminal Input */}
          <div className="border-t border-green-500/30 p-4 bg-gray-900">
            <form onSubmit={handleSubmit} className="flex items-center space-x-2">
              <span className="text-green-400 font-mono">$</span>
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                disabled={isTyping}
                className="flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-green-500/50"
                placeholder="Type a command..."
                onClick={(e) => e.currentTarget.select()}
              />
              {cursorVisible && (
                <span className="w-2 h-4 bg-green-400 inline-block"></span>
              )}
            </form>
          </div>
        </Card>

        {/* Quick Commands */}
        <div className="mt-6 flex flex-wrap gap-2 justify-center">
          {portfolioSections.map(section => (
            <Button
              key={section.id}
              variant="outline"
              size="sm"
              onClick={() => handleQuickCommand(section.commands?.[0] || section.id)}
              disabled={isTyping}
              className="border-green-500/30 text-green-400 hover:bg-green-500/10 hover:text-green-300 transition-all duration-200"
            >
              {section.title}
            </Button>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickCommand('help')}
            disabled={isTyping}
            className="border-green-500/30 text-green-400 hover:bg-green-500/10 hover:text-green-300 transition-all duration-200"
          >
            Help
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm font-mono">
            Press Tab to focus • Type commands to navigate • ESC to clear
          </p>
        </div>
      </div>
    </div>
  )
}